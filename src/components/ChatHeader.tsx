import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import CodeIcon from "@/assets/code.svg";
import CodeHoverIcon from "@/assets/code-hover.svg";
import DiffIcon from "@/assets/diff.svg";
import InfoIcon from "@/assets/info.svg";
import PreviewIcon from "@/assets/eye.svg";
import HistoryIcon from "@/assets/history.svg";
import { MoreHorizontal, MoreVertical } from "lucide-react";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useState, useEffect, useMemo } from "react";
import { ChatHistoryModal } from "./modals/ChatHistoryModal";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";
import { useDeploy } from "@/hooks/useDeploy";
import { Loader2 } from "lucide-react";
// @ts-ignore
import animatedSpinner from "../assets/animated-spinner.gif";
import { useTabState } from "./TabBar";
import DeployCloud from "@/assets/DeployCloud.svg";
import { cn } from "@/lib/utils";
import PulseDot from "./PulseDot";
import Redeploy from "@/assets/RedeployNew.svg"
import useScreenSize from "@/hooks/useScreenSize";
import GlobeIcon from "./icons/GlobeIcon";
import DoubleCheckIcon from "./icons/DoubleCheckIcon";

interface PublishConfig {
  enablePublish?: boolean;
  isPublishing?: boolean;
  isPublished?: boolean;
}

interface ChatHeaderProps {
  handleVSCodeLink: () => void;
  handleShowDiff: () => void;
  handlePreviewClick: () => void;
  handleInfoClick: () => void;
  handleDeployClick?: () => void;
  handlePublishClick?: () => void;
  showActions: boolean;
  hideImportantActions: boolean;
  jobId?: string;
  podIsPaused?: boolean;
  enableDeploy?: boolean;
  publishConfig?: PublishConfig;
  previewUrl?: string;
  buildMode:  "brainstorming_requested" | "brainstorming_done" | "build" | null;
}

export const ChatHeader = ({
  handleVSCodeLink,
  handleShowDiff,
  handlePreviewClick,
  handleInfoClick,
  handleDeployClick,
  handlePublishClick,
  showActions,
  hideImportantActions,
  jobId,
  podIsPaused = false,
  enableDeploy = true,
  publishConfig = {},
  previewUrl,
  buildMode,
}: ChatHeaderProps) => {
  // Destructure publish config with defaults
  const {
    enablePublish = false,
    isPublishing = false,
    isPublished = false
  } = publishConfig;
  const [isChatHistoryModalOpen, setIsChatHistoryModalOpen] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isPublishPopoverOpen, setIsPublishPopoverOpen] = useState(false);
  const isEmergentUser = useIsEmergentUser();
  const {
    deployStatus,
    latestRunStatus,
    loading,
    checkDeployStatus,
    loadDeploymentHistory,
  } = useDeploy(jobId);
  const {isMobile} = useScreenSize();

  const { getTabByJobId } = useTabState();

  const isShowCase = getTabByJobId(jobId || "")?.state?.showCase || false;

  // Memoize the tooltip style to prevent re-renders
  const tooltipStyle = useMemo(() => ({
    fontFamily: "Inter",
    fontWeight: 500
  }), []);

  // Computed visibility conditions to simplify rendering logic
  const shouldShowDeployButton = useMemo(() => {
    return jobId &&
           handleDeployClick &&
           !enablePublish &&
           !hideImportantActions &&
           !isShowCase;
  }, [jobId, handleDeployClick, enablePublish, hideImportantActions, isShowCase]);

  const shouldShowPublishButton = useMemo(() => {
    return jobId &&
           handlePublishClick &&
           !hideImportantActions &&
           !isShowCase &&
           enablePublish;
  }, [jobId, handlePublishClick, hideImportantActions, isShowCase, enablePublish]);

  const shouldShowMobileDeployButton = useMemo(() => {
    return jobId &&
           !enablePublish &&
           handleDeployClick &&
           !hideImportantActions &&
           !podIsPaused &&
           !isShowCase;
  }, [jobId, enablePublish, handleDeployClick, hideImportantActions, podIsPaused, isShowCase]);

  const shouldShowMobilePublishButton = useMemo(() => {
    return jobId &&
           handlePublishClick &&
           !hideImportantActions &&
           !isShowCase &&
           enablePublish;
  }, [jobId, handlePublishClick, hideImportantActions, isShowCase, enablePublish]);

  useEffect(() => {
    if (jobId && !isShowCase  && buildMode == "build") {
      checkDeployStatus(jobId, true);

      if (deployStatus !== "not_deployed") {
        loadDeploymentHistory();
      }
    }
  }, [jobId, isShowCase, podIsPaused, checkDeployStatus, deployStatus, loadDeploymentHistory, buildMode]);

  const deployButtonText = () => {
    if (loading) {
      return "Loading...";
    } else if (deployStatus === "running" || latestRunStatus === "running") {
      return "Deploying...";
    } else if (deployStatus === "success") {
      return "Redeploy";
    } else {
      return "Deploy";
    }
  };

  const handleMenuItemClick = (action: () => void) => {
    action();
    setIsMenuOpen(false);
  };

  return (
    <>
      <div className="flex items-center gap-4">
        <div className="flex-1" />

        <div className="flex items-center">
          {showActions && !hideImportantActions && (
            <div className="flex items-center gap-1">
              <TooltipProvider>
                <div className="flex items-center gap-1">
                  {isEmergentUser && jobId && (
                    <Tooltip delayDuration={100}>
                      {/* <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          className="group h-[36px] w-[36px] p-0 hover:bg-white/5 rounded-lg transition-colors duration-200"
                          onClick={() => setIsChatHistoryModalOpen(true)}
                        >
                          <div className="flex items-center justify-center w-full">
                            <img
                              src={HistoryIcon}
                              alt="chat history"
                              className="w-6 h-6 opacity-50 group-hover:opacity-100"
                            />
                          </div>
                        </Button>
                      </TooltipTrigger> */}
                      <TooltipContent
                        side="bottom"
                        className="bg-[#DDDDE6] text-black border-0"
                        style={tooltipStyle}
                      >
                        Edit Chat History
                      </TooltipContent>
                    </Tooltip>
                  )}

                  {!isMobile && <Tooltip delayDuration={100}>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        className="group h-[36px] w-[36px] p-0 bg-white/5 hover:bg-[#ffffff08] backdrop-blur-lg rounded-lg transition-colors duration-200"
                        onClick={handleInfoClick}
                      >
                        <div className="flex items-center justify-center w-full">
                          <img
                            src={InfoIcon}
                            alt="info"
                            className="w-6 h-6 opacity-50 group-hover:opacity-100"
                          />
                        </div>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent
                      side="bottom"
                      className="bg-[#DDDDE6] text-black border-0"
                      style={tooltipStyle}
                    >
                      Info
                    </TooltipContent>
                  </Tooltip>}
                </div>
              </TooltipProvider>
            </div>
          )}

          {showActions && (
            <>
               {!isMobile && <>
               <div className="">
                 <Button
                    variant="default"
                    className="group h-[36px] ml-2 pl-2 pr-2 md:pr-3 rounded-[8px] hover:bg-white/100 transition-colors duration-200"
                    onClick={handleVSCodeLink}
                    disabled={podIsPaused || buildMode != "build"}
                  >
                    <div className="flex items-center gap-1">
                      <img
                        alt="code"
                        src={CodeIcon}
                        className="w-6 h-6"
                      />
                      <span className="hidden text-base font-semibold md:block">
                        Code
                      </span>
                    </div>
                  </Button>
               </div>

              <div className="ml-2">
                <Button
                  variant="default"
                  className="group h-[36px] pl-2 pr-2 md:pr-3 rounded-[8px] hover:bg-white/100 transition-colors duration-200"
                  onClick={handlePreviewClick}
                  disabled={buildMode != "build"}
                >
                  <div className="flex items-center gap-1">
                    <img alt="preview" src={PreviewIcon} className="w-6 h-6" />
                    <span className="hidden text-base font-semibold md:block">
                      Preview
                    </span>
                  </div>
                </Button>
              </div>

              {shouldShowDeployButton && (
                  <div className="ml-2">
                    <Button
                      variant="ghost"
                      className={cn(
                        `group h-[36px] pl-2 pr-2 md:pr-3  rounded-[8px] hover:bg-white/10 transition-colors duration-200`,
                        deployStatus === "success"
                          ? "bg-white/10 grayscale-0"
                          : deployStatus === "running" ||
                            latestRunStatus === "running"
                          ? "bg-[#4DA5FF29] text-[#33DDFF]"
                          : "bg-gradient-to-r from-[#80FFF9] to-[#1588FC] text-[#0F0F10] hover:text-[#0F0F10] hover:opacity-80",
                        (loading || !enableDeploy) &&
                          deployStatus == "not_deployed" &&
                          "opacity-90 grayscale", "disabled:grayscale"
                      )}
                      onClick={handleDeployClick}
                      disabled={
                        (loading || !enableDeploy) &&
                        deployStatus == "not_deployed"
                      }
                    >
                      <div className="relative flex items-center gap-1">
                        {loading ? (
                          <Loader2 className="w-5 h-5 animate-spin" />
                        ) : deployStatus === "running" ||
                          latestRunStatus === "running" ? (
                          <img
                            src={animatedSpinner}
                            alt="deploying"
                            className="w-5 h-5"
                          />
                        ) : (
                         <div className="relative">
                          <img
                            src={deployStatus == "success" ? Redeploy : DeployCloud}
                            alt="Deploy"
                            className={cn("w-5 h-5 ", deployStatus != "success" && "invert")}
                          />
                          {deployStatus == "success" && <PulseDot
                            color="#2EE572"
                            size={16}
                            innerSize={8}
                            animate={deployStatus === "success"}
                            className="absolute -bottom-[4px] -right-[4.5px]"
                          />}
                         </div>
                        )}
                        <span className="hidden text-base font-semibold md:block">
                          {deployButtonText()}
                        </span>
                      </div>
                    </Button>
                  </div>
                )}

              {/* Publish Button */}
              {shouldShowPublishButton && (
                  <div className="ml-2">
                    <Popover open={isPublishPopoverOpen} onOpenChange={setIsPublishPopoverOpen}>
                      <PopoverTrigger asChild>

                        {!isPublished ? <Button
                          variant="ghost"
                          className="group h-[36px] py-2 px-3 rounded-[8px] bg-[#0098FE] hover:bg-[#0098FE]/80 text-white transition-opacity duration-200"
                          disabled={isPublishing || !enableDeploy}
                        >
                          <div className="flex items-center gap-1">
                            {isPublishing ? (
                              <Loader2 className="w-5 h-5 animate-spin" />
                            ) : null}
                            <span className="hidden text-sm font-semibold md:block">
                              {isPublishing ? "Publishing..." : "Publish"}
                            </span>
                          </div>
                        </Button> : <Button
                          variant="ghost"
                          className="group h-[36px] py-2 px-3 rounded-[8px] bg-[#80CCFF33] hover:bg-[#80CCFF33] text-white transition-opacity duration-200"
                        >
                          <div className="flex items-center gap-[6px]">
                            <DoubleCheckIcon width={16} height={16} />
                            <span className="hidden text-sm font-semibold md:block">
                              Published
                            </span>
                          </div>
                        </Button>}
                        

                      </PopoverTrigger>
                      <PopoverContent
                        className="w-[400px] mr-6 mt-4 p-4 bg-[#222224] border-none rounded-[16px] text-white"
                        onOpenAutoFocus={(e) => e.preventDefault()}
                        onCloseAutoFocus={(e) => e.preventDefault()}
                        onEscapeKeyDown={(e) => {
                          setIsPublishPopoverOpen(false);
                        }}
                        onPointerDownOutside={(e) => {
                          setIsPublishPopoverOpen(false);
                        }}
                        onFocusOutside={(e) => {
                          e.preventDefault();
                        }}
                        onInteractOutside={(e) => {
                          e.preventDefault();
                        }}
                      >
                        <div className="space-y-5">
                          <div className="flex flex-col space-y-2">
                          {previewUrl && (
                            <div className="flex items-center gap-[6px] p-3 bg-[#FFFFFF0F] rounded-[10px]">
                              <div className="w-6 h-6 rounded-full flex items-center justify-center">
                                <GlobeIcon width={20} height={20} />
                              </div>
                              <span className="text-sm text-white font-medium font-['Inter'] truncate flex-1">{previewUrl}</span>
                            </div>
                          )}
                            <p className={cn("py-2 rounded-[10px] font-['Inter'] text-sm font-medium mb-4", isPublished ? "text-white/30 bg-transparent" : "text-[#FFAE66] bg-[#FFAE660A] px-3")}>
                              Preview links expire in 30 minutes. Publish your app to make your preview link persistent.
                            </p>
                          </div>
                          { !isPublished && <div className="text-start">
                            <Button
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePublishClick?.();
                              }}
                              disabled={isPublishing}
                              className="w-full h-12 bg-gradient-to-b from-[#33CCFF] to-[#337AF2] hover:opacity-90 text-white font-semibold rounded-[10px]"
                            >
                              <div className="flex items-center justify-center gap-2 w-full">
                              {isPublishing ? (
                                <>
                                  <Loader2 className="w-5 h-5 animate-spin mr-2" />
                                  Publishing...
                                </>
                              ) : (
                                "Publish"
                              )}
                              </div>
                            </Button>
                          </div>}
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                )}
               </>}

              {/* 3 Dots Menu */}
              <div className="max-w-[48px] block md:hidden">
                <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                  <DropdownMenuTrigger asChild>
                    <div className="h-6 pb-0 pr-3">
                    <MoreHorizontal className="w-6 h-6" />
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent 
                    align="end" 
                    className="w-48 mr-4 bg-[#1A1A1B] border-[#333333] text-white"
                  >
                    {/* Chat History - Only for emergent users */}
                    {/* {isEmergentUser && jobId && (
                      <DropdownMenuItem
                        onClick={() => handleMenuItemClick(() => setIsChatHistoryModalOpen(true))}
                        className="flex items-center gap-2 hover:bg-white/10 focus:bg-white/10"
                      >
                        <img src={HistoryIcon} alt="chat history" className="w-4 h-4" />
                        <span>Edit Chat History</span>
                      </DropdownMenuItem>
                    )} */}

                    {/* Info */}
                    <DropdownMenuItem
                      onClick={() => handleMenuItemClick(handleInfoClick)}
                      className=" h-12 w-12 flex px-2 py-0 items-center gap-2 hover:bg-black/5 focus:bg-black/5 w-full"
                    >
                      {/* <img src={InfoIcon} alt="info" className="w-4 h-4 rounded-sm" /> */}
                      <img src={InfoIcon} alt="info" className="w-5 h-5 rounded-sm" />
                      <span>Info</span>
                    </DropdownMenuItem>

                    {/* Code */}
                    <DropdownMenuItem
                      onClick={() => handleMenuItemClick(handleVSCodeLink)}
                      disabled={podIsPaused || buildMode != "build"}
                      className="group h-12 flex px-2 py-0 items-center gap-2 hover:bg-black/5 focus:bg-black/5 w-full"
                    >
                      <img src={CodeIcon} alt="code" className="w-5 h-5 bg-[#B8B8CC] rounded-sm" />
                      <span>Code</span>
                    </DropdownMenuItem>

                    {/* Preview */}
                    <DropdownMenuItem
                      onClick={() => handleMenuItemClick(handlePreviewClick)}
                      disabled={buildMode != "build"}
                      className="group h-12 px-2 py-0 flex items-center gap-2 rounded-[8px] hover:bg-black/5 focus:bg-black/5 transition-colors duration-200 w-full"
                    >
                      <img src={PreviewIcon} alt="preview" className="w-5 h-5 bg-[#B8B8CC] rounded-sm" />
                      <span>Preview</span>
                    </DropdownMenuItem>

                    {/* Deploy */}
                    {shouldShowMobileDeployButton && (<DropdownMenuItem
                      onClick={() => handleMenuItemClick(handleDeployClick!)}
                      disabled={
                        (loading || !enableDeploy) &&
                        deployStatus == "not_deployed"
                      }
                      className={cn(
                        `group h-10 px-2 py-0 flex items-center gap-2 rounded-[8px] hover:bg-black/5 focus:bg-black/5 transition-colors duration-200 w-full`,
                        deployStatus === "success"
                          ? "bg-white/10 grayscale-0"
                          : deployStatus === "running" ||
                            latestRunStatus === "running"
                          ? "bg-[#4DA5FF29] text-[#33DDFF]"
                          : "bg-gradient-to-r from-[#80FFF9] to-[#1588FC] text-[#fff] hover:text-[#0F0F10] hover:opacity-80",
                        (loading || !enableDeploy) &&
                          deployStatus == "not_deployed" &&
                          "opacity-90 grayscale text-background"
                      )}
                    >
                      <div className="relative flex items-center gap-2">
                        {loading ? (
                          <Loader2 className="w-5 h-5 animate-spin" />
                        ) : deployStatus === "running" ||
                          latestRunStatus === "running" ? (
                          <img
                            src={animatedSpinner}
                            alt="deploying"
                            className="w-5 h-5"
                          />
                        ) : (
                         <div className="relative">
                          <img
                            src={deployStatus == "success" ? Redeploy : DeployCloud}
                            alt="Deploy"
                            className={cn("w-5 h-5 ", deployStatus != "success" && "invert")}
                          />
                          {deployStatus == "success" && <PulseDot
                            color="#2EE572"
                            size={16}
                            innerSize={8}
                            animate={deployStatus === "success"}
                            className="absolute -bottom-[4px] -right-[4.5px]"
                          />}
                         </div>
                        )}
                        <span className={`${deployButtonText() == "Deploy" ? "text-background text-sm" : "text-white text-sm"}`}>
                          {deployButtonText()}
                        </span>
                      </div>
                    </DropdownMenuItem>)}

                    {/* Publish Button */}
                    {shouldShowMobilePublishButton && (
                        <DropdownMenuItem
                          onClick={() => {
                            setIsMenuOpen(false);
                            setIsPublishPopoverOpen(true);
                          }}
                          disabled={isPublishing}
                          className="h-12 w-12 flex px-2 py-0 items-center gap-2 hover:bg-black/5 focus:bg-black/5 w-full"
                        >
                          <div className="flex items-center gap-2">
                            {isPublishing ? (
                              <Loader2 className="w-5 h-5 animate-spin" />
                            ) : (
                              <svg
                                className="w-5 h-5"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            )}
                            <span className="text-white text-sm">
                              {isPublishing ? "Publishing..." : "Publish"}
                            </span>
                          </div>
                        </DropdownMenuItem>
                      )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </>
          )}
        </div>
      </div>

      <ChatHistoryModal
        isOpen={isChatHistoryModalOpen}
        onOpenChange={setIsChatHistoryModalOpen}
        chatId={jobId}
      />
    </>
  );
};