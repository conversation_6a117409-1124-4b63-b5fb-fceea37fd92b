import { useState, useEffect, useRef } from "react";
import { Minus, Plus, Loader2 } from "lucide-react";
import { AnimatePresence, motion } from "motion/react"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

import debounce from "lodash.debounce";
import { useToast } from "@/hooks/use-toast";
import CuteAIIcon from "@/assets/cute-ai.svg";
import * as <PERSON>lt<PERSON> from "@radix-ui/react-tooltip";
import { useAuth, useCredits } from "@/contexts";
import DockerImageSelector from "@/components/DockerImageSelector";
import SkillsSelector from "@/components/SkillsSelector";
import AttachmentCross from "@/assets/attachment-cross.svg";
import AttachIcon from "@/assets/attach.svg";
import { useImageAttachments } from "@/hooks/useImageAttachments";
import { useImageCompression } from "@/hooks/useImageCompression";
import { processFilesForDualMode } from "@/lib/utils/fileProcessing";
import { Base64Image, DEFAULT_COMPRESSION_SETTINGS } from "@/lib/types/fileTypes";
import TextareaWithAttachmentV2 from "@/components/TextareaWithAttachmentV2";
import AuthModal from "@/components/modals/AuthModal";
import SuggestionChips from "@/components/SuggestionChips";
import { trackTaskCreation, trackUserSource, trackExperimentalFeature, isInputDisabled, isReferralEnabled, isE1Enabled, useIsUploadAssetEnabled, isCustomAgentsEnabled } from "@/services/postHogService";

// Assets
import AdvancedControls from "@/assets/advanced-controls.svg"
import CloseSVG from "@/assets/close.svg"
import { useTabState } from "@/components/TabBar";
import CoinIconSVG from "@/assets/copper-coin.svg"
import { useGitHub } from "@/hooks/useGitHubAPI";
import { useGetConfigQuery } from "@/store/api/apiSlice";
import { RepositorySelectorV2, RepositorySelectorRef } from "@/components/RepositorySelectorV2";
import useScreenSize from "@/hooks/useScreenSize";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";
import { useProModePayment } from "@/hooks/useProModePayment";
import { useGetUserPromptsQuery } from "@/store/api/promodeApiSlice";

import ReferralBanner from "@/components/ReferralBanner";
import CustomAgentsBanner from "@/components/CustomAgentsBanner";
import { CustomAgentsModal } from "@/components/modals/CustomAgentsModal";
import { cn } from "@/lib/utils";

interface Skill {
    id: string;
    name: string;
    latest_version: string;
    job_id: string;
    created_at: string;
    updated_at: string;
}

interface TaskFormState {
    taskInput: string;
    selectedImage: string;
    selectedAgent: string;
    selectedModel: string;
    perInstanceCostLimit: string;
    selectedSkills: string[];
    selectedImages: Array<{
        mime_type: string;
        img_base64: string;
    }>;
    loadingImages: boolean;
    githubUrl: string;
    branchName: string;
    projectType: string;
    selectedCustomAgent: string | null; // New field for custom agent selection
}

interface CreateTaskProps {
    tabId: string;
    onClearTabId: () => void
    isReferralModalOpen: boolean;
    setIsReferralModalOpen: (open: boolean) => void;
    isCustomAgentsModalOpen: boolean;
    setIsCustomAgentsModalOpen: (open: boolean) => void;
}

export default function EmbeddedTask({ tabId, onClearTabId, isReferralModalOpen, setIsReferralModalOpen, isCustomAgentsModalOpen, setIsCustomAgentsModalOpen }: CreateTaskProps) {
    const { toast } = useToast();
    const { data: globalConfig, isLoading: configLoading } = useGetConfigQuery();
    const { getTabState, updateTabState, setTabs, setActiveTab } = useTabState();
    const { session, user } = useAuth();
    const { tier } = useCredits();
    const { isConnected: hasUserConnectedGithub } = useGitHub();

    // Pro mode and emergent user checks
    const isEmergentUser = useIsEmergentUser();
    const { isProModeUser } = useProModePayment();

    // Asset upload functionality
    const isUploadAssetEnabled = useIsUploadAssetEnabled();
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]); // Store files locally until job submission
    const [isProcessingFiles, setIsProcessingFiles] = useState(false);

    // Initialize image compression hook
    const { compressImage } = useImageCompression(DEFAULT_COMPRESSION_SETTINGS);

    // Fetch user prompts only if user is emergent user and has pro mode enabled
    const shouldFetchUserPrompts = isEmergentUser;
    const { data: userPromptsData, isLoading: isLoadingUserPrompts } = useGetUserPromptsQuery(
        undefined,
        { skip: !shouldFetchUserPrompts }
    );

    // Centralized form state
    const [formState, setFormState] = useState<TaskFormState>({
        taskInput: "",
        selectedImage: "",
        selectedAgent: "",
        selectedModel: "",
        perInstanceCostLimit: "5",
        selectedSkills: [],
        selectedImages: [],
        loadingImages: false,
        githubUrl: "",
        branchName: "",
        projectType: "new_app",
        selectedCustomAgent: null
    });

    // UI state
    const [showControls, setShowControls] = useState(false);
    const [showGithubSettings, setShowGithubSettings] = useState(false);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const [showChips, setShowChips] = useState(true);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentBanner, setCurrentBanner] = useState<'referral' | 'customAgents'>('referral');
    const [isBannerHovered, setIsBannerHovered] = useState(false);
    const [bannerProgress, setBannerProgress] = useState(0);

    // Helper function to get default agent based on user tier
    const getDefaultAgentForTier = () => {
        return tier === "free" && !isE1Enabled();
    };

    const [experimental, setExperimental] = useState(() => getDefaultAgentForTier());
    const [skills, setSkills] = useState<Skill[]>([]);
    const { isMobile } = useScreenSize();

    // Refs
    const submissionInProgress = useRef(false);
    const repositorySelectorRef = useRef<RepositorySelectorRef>(null);
    const initializedTabs = useRef<Set<string>>(new Set());
    const referralEnabled = isReferralEnabled();
    const customAgentsEnabled = isCustomAgentsEnabled();

    const [mobileEnabled, setMobileEnabled] = useState(false);

    // Centralized state update function
    const updateFormState = (updates: Partial<TaskFormState>) => {
        setFormState(prev => ({ ...prev, ...updates }));
    };

    // Track user source when component mounts
    useEffect(() => {
        trackUserSource({
            component: 'EmbeddedTask',
            userId: user?.id,
            isAuthenticated: !!session
        });
    }, [user?.id, session]);

    // Initialize form state from config and tab state
    useEffect(() => {
        if (!tabId || !globalConfig) return;

        // Only initialize tab state once per tab ID
        if (initializedTabs.current.has(tabId)) return;

        const tabState = getTabState(tabId);
        const formLayout = globalConfig.form_layouts?.new_app?.cloud;
        const defaults = formLayout?.defaults || {};

        // Initialize form state with defaults and tab state
        updateFormState({
            taskInput: tabState.task || defaults.taskDescription || "",
            selectedImage: tabState.selectedImage || defaults.dockerImage || "",
            selectedAgent: tabState.selectedAgent || defaults.agent || "",
            selectedModel: tabState.model_name || defaults.baseModel || "",
            perInstanceCostLimit: tabState.per_instance_cost_limit || defaults.budget?.toString() || "5",
            selectedSkills: tabState.agentic_skills || defaults.skills || [],
            selectedImages: tabState.selectedImages || [],
            githubUrl: tabState.repository || "",
            branchName: tabState.branch || "",
            projectType: tabState.projectType || "new_app",
            selectedCustomAgent: tabState.selectedCustomAgent || null
        });

        // Mark this tab as initialized
        initializedTabs.current.add(tabId);
    }, [globalConfig, getTabState, tabId]);

    // Update tab state when form state changes
    useEffect(() => {
        if (!tabId || initializedTabs.current.size === 0) return;

        const updateData = {
            task: formState.taskInput,
            selectedImage: formState.selectedImage,
            image: formState.selectedImage, // Duplicate for backward compatibility
            selectedAgent: formState.selectedAgent,
            model_name: formState.selectedModel,
            per_instance_cost_limit: formState.perInstanceCostLimit,
            agentic_skills: formState.selectedSkills,
            selectedImages: formState.selectedImages,
            repository: formState.githubUrl,
            branch: formState.branchName,
            projectType: formState.projectType,
            selectedCustomAgent: formState.selectedCustomAgent,
            isCloudFlow: true // Always cloud
        };

        updateTabState(tabId, updateData);
    }, [formState, tabId, updateTabState]);

    useEffect(() => {
        if (mobileEnabled) {
            updateFormState({
                selectedAgent: globalConfig?.agent_names?.find((a) => a.name === "expo_fullstack_v0")?.name || "",
                selectedImage: globalConfig?.templates?.find((t) => t.id === "expo")?.env_image || ""
            })
        }
    }, [mobileEnabled])

    // Initialize skills from config
    useEffect(() => {
        if (globalConfig?.skills) {
            setSkills(globalConfig.skills);
        }
    }, [globalConfig]);

    // Update selected skills when agent changes
    useEffect(() => {
        if (formState.selectedAgent && globalConfig) {
            const selectedAgentConfig = globalConfig.agent_names?.find(
                (agent: any) => agent.name === formState.selectedAgent
            );

            if (selectedAgentConfig?.sub_agents) {
                const validSubAgents = selectedAgentConfig.sub_agents.filter(
                    (subAgent: any) => skills.some((skill) => skill.name === subAgent)
                );
                updateFormState({ selectedSkills: validSubAgents });
            } else {
                updateFormState({ selectedSkills: [] });
            }
        }
    }, [formState.selectedAgent, skills, globalConfig]);

    // Show chips again when textarea is cleared
    useEffect(() => {
        if (formState.taskInput === "" && !showChips) {
            setShowChips(true);
        }
    }, [formState.taskInput, showChips]);

    // Initialize experimental features based on user tier
    useEffect(() => {
        if (!user) return;

        // Use tier-based default for agent selection
        const tierBasedDefault = getDefaultAgentForTier();
        setExperimental(tierBasedDefault);

        trackExperimentalFeature(tierBasedDefault, {
            userId: user.id,
            component: 'EmbeddedTask',
            tier: tier
        });
    }, [user, tier]);

    // Banner switching animation - only if both banners are enabled
    useEffect(() => {
        if (!referralEnabled || !customAgentsEnabled || isBannerHovered) {
            setBannerProgress(0);
            return;
        }

        let startTime = Date.now();
        const duration = 5000; // 5 seconds

        const progressInterval = setInterval(() => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min((elapsed / duration) * 100, 100);
            setBannerProgress(progress);
        }, 50); // Update progress every 50ms for smooth animation

        const switchInterval = setInterval(() => {
            setCurrentBanner(prev => prev === 'referral' ? 'customAgents' : 'referral');
            setBannerProgress(0);
            startTime = Date.now(); // Reset start time for next cycle
        }, duration);

        return () => {
            clearInterval(progressInterval);
            clearInterval(switchInterval);
        };
    }, [referralEnabled, customAgentsEnabled, isBannerHovered]);

    // Initialize tab
    useEffect(() => {
        setTabs((prevTabs) => {
            const tabExists = prevTabs.some(tab => tab.id === tabId);
            if (!tabExists) {
                return [
                    ...prevTabs,
                    {
                        id: tabId,
                        title: "Setting up Task",
                        path: "/not-defined",
                        state: {
                            tabId: tabId,
                            sourceTabId: tabId,
                            isCloudFlow: true,
                        },
                        status: false,
                        isCloudFlow: true,
                    },
                ];
            }
            return prevTabs;
        });

        updateTabState(tabId, { isCloudFlow: true });
    }, [tabId]);

    // Image attachments hook
    const imageAttachments = useImageAttachments({
        maxImages: 5,
        maxSizeInMB: 5,
        maxPixelDimensions: 8000,
        maxPixelDimensionsMultiple: 2000,
        onImagesChange: (images) => updateFormState({ selectedImages: images })
    });

    // Set loading state for images
    useEffect(() => {
        updateFormState({ loadingImages: imageAttachments.isProcessing });
    }, [imageAttachments.isProcessing]);

    // Event handlers
    const handleLoginSuccess = (session: any) => {
        trackUserSource({
            component: 'EmbeddedTask',
            userId: session?.user?.id,
            isAuthenticated: true,
            loginSource: 'task_creation'
        });
    };

    const handleChipClick = (prompt: string) => {
        updateFormState({ taskInput: prompt });
        setExperimental(true);
    };

    const handleExperimentChange = (value: boolean) => {
        setExperimental(value);
        trackExperimentalFeature(value, {
            userId: user?.id,
            component: 'EmbeddedTask',
            source: 'manual_toggle'
        });
    };

    const onClearGithub = () => {
        updateFormState({ githubUrl: '', branchName: '' });
        if (repositorySelectorRef.current) {
            repositorySelectorRef.current.reset();
        }
    };

    // Handle file selection when uploadAssetEnabled is true (store files locally, don't upload yet)
    const handleAssetSelection = (files: FileList | File[]) => {
        if (!isUploadAssetEnabled || !files || files.length === 0) return;

        const filesArray = Array.from(files);

        // Check if adding these files would exceed the 5 attachment limit
        if (selectedFiles.length + filesArray.length > 5) {
            toast({
                title: "Too many attachments",
                description: "Maximum 5 attachments allowed",
                variant: "destructive",
            });
            return;
        }

        // Check file sizes (200MB = 200 * 1024 * 1024 bytes)
        const maxSizeBytes = 200 * 1024 * 1024;
        const oversizedFiles = filesArray.filter(file => file.size > maxSizeBytes);

        if (oversizedFiles.length > 0) {
            toast({
                title: "File too large",
                description: `Files must be smaller than 200MB. ${oversizedFiles.length} file(s) exceed this limit.`,
                variant: "destructive",
            });
            return;
        }

        const newSelectedFiles = [...selectedFiles, ...filesArray];
        setSelectedFiles(newSelectedFiles);

        // Store selected files in tab state so ChatScreen can access them
        const currentTabState = getTabState(tabId);
        updateTabState(tabId, {
            ...currentTabState,
            selectedFiles: newSelectedFiles
        });

        toast({
            title: "Files selected",
            description: `${filesArray.length} file(s) selected for upload`,
        });
    };

    // Unified file picker that handles both legacy image attachments and new asset uploads
    const handleFilePicker = () => {
        if (isUploadAssetEnabled) {
            // Create file input for all file types
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.multiple = true;
            fileInput.style.display = 'none';

            fileInput.onchange = (e) => {
                const target = e.target as HTMLInputElement;
                if (target.files) {
                    handleAssetSelection(target.files);
                }
                document.body.removeChild(fileInput);
            };

            document.body.appendChild(fileInput);
            fileInput.click();
        } else {
            // Use legacy image attachment flow
            imageAttachments.openFilePicker();
        }
    };

    // Remove selected file
    const handleRemoveSelectedFile = (index: number) => {
        const newSelectedFiles = selectedFiles.filter((_, i) => i !== index);
        setSelectedFiles(newSelectedFiles);

        // Update tab state
        const currentTabState = getTabState(tabId);
        updateTabState(tabId, {
            ...currentTabState,
            selectedFiles: newSelectedFiles
        });
    };

    // Add this helper function near the top of the component
    const isFieldVisible = (fieldId: string) => {
        // @ts-ignore
        const formLayout = globalConfig?.form_layouts?.[formState.projectType]?.cloud;
        const fieldConfig = formLayout?.fields?.find((field : any) => field.id === fieldId);
        return fieldConfig?.show !== false; // Default to true if not explicitly set to false
    };

    const debouncedSubmit = debounce(async () => {
        try {
            if (submissionInProgress.current) return;

            submissionInProgress.current = true;
            setIsSubmitting(true);

            console.log("EmbeddedTask - Starting task submission for tabId:", tabId);

            // Track task submission
            trackTaskCreation('submitted', {
                userId: user?.id,
                projectType: formState.projectType,
                isCloudFlow: true,
                taskInputLength: formState.taskInput.length,
                taskInput: formState.taskInput.substring(0, 100),
                hasImages: formState.selectedImages.length > 0,
                imageCount: formState.selectedImages.length,
                selectedModel: formState.selectedModel,
                selectedAgent: formState.selectedAgent,
                budget: formState.perInstanceCostLimit,
                tabId
            });

            if (!formState.selectedImage || !formState.taskInput) {
                toast({
                    title: "Error",
                    description: "Please fill in all required fields",
                    variant: "destructive",
                });
                return;
            }

            // Validate GitHub repository and branch selection
            if (formState.githubUrl && !formState.branchName) {
                toast({
                    title: "Branch Required",
                    description: "Please select a branch for the GitHub repository",
                    variant: "destructive",
                });
                return;
            }

            // Process selected files using utility function
            let base64Images: Base64Image[] = [];
            let filesToUploadAsArtifacts: File[] = [];

            if (selectedFiles && selectedFiles.length > 0) {
                console.log('EmbeddedTask - Processing selected files for submission:', selectedFiles.length);
                setIsProcessingFiles(true);

                try {
                    const result = await processFilesForDualMode(
                        selectedFiles,
                        compressImage,
                        (current, total) => {
                            console.log(`EmbeddedTask - Processing file ${current}/${total}`);
                        }
                    );

                    base64Images = result.base64Images;
                    filesToUploadAsArtifacts = result.filesToUploadAsArtifacts;
                } finally {
                    setIsProcessingFiles(false);
                }
            }

            // Combine legacy selectedImages with new base64Images from compressed files
            const allBase64Images = [
                ...(formState.selectedImages || []),
                ...base64Images
            ];

            const tabStateData = {
                // Core form data with consistent field names
                task: formState.taskInput,
                selectedImage: formState.selectedImage,
                image: formState.selectedImage, // Duplicate for backward compatibility
                selectedAgent: formState.selectedAgent,
                model_name: formState.selectedModel,
                per_instance_cost_limit: formState.perInstanceCostLimit,
                agentic_skills: formState.selectedSkills,
                selectedImages: allBase64Images,
                repository: formState.githubUrl,
                branch: formState.branchName || '',
                projectType: formState.projectType,
                selectedCustomAgent: formState.selectedCustomAgent,

                // Tab management fields
                fromCreateTask: true,
                tabId: tabId,
                parentTabId: tabId,
                isCloudFlow: true,
                client_ref_id: null,
                needsApiCall: true,
                pending_client_ref_id: crypto.randomUUID(),
                id: tabId,
                title: "Setting up Task",
                path: "/chat",
                experimental: experimental,
                selectedFiles: filesToUploadAsArtifacts, // Only files that need to be uploaded as artifacts

                // Image data
                base64_image_list: allBase64Images.length > 0 ? allBase64Images : undefined,

                // State metadata
                state: {
                    tabId: tabId,
                    sourceTabId: tabId,
                },
            };

            // Add custom agent handling
            const finalTabStateData = {
                ...tabStateData,
                // Custom agent handling - replace prompt_name with user_prompt_id if custom agent is selected
                ...(formState.selectedCustomAgent ? {
                    user_prompt_id: formState.selectedCustomAgent,
                    // Don't include prompt_name when using custom agent
                } : {
                    prompt_name: "auto_prompt_selector_dev", // Default prompt name
                }),
            };

            console.log('EmbeddedTask - Updated tab state (DUAL MODE - artifacts + base64):', {
                base64ImagesCount: allBase64Images.length,
                artifactFilesCount: filesToUploadAsArtifacts.length,
                totalSelectedFiles: selectedFiles.length,
                base64Images: allBase64Images.map(img => ({
                    mime_type: img.mime_type,
                    base64_length: img.img_base64?.length || 0,
                    base64_preview: img.img_base64?.substring(0, 50) + '...'
                })),
                artifactFiles: filesToUploadAsArtifacts.map(f => ({ name: f.name, type: f.type, size: f.size })),
                note: "ALL files go to artifacts, some ALSO get base64 if under 5MB"
            });

            // Clear images from both the hook and local form state
            imageAttachments.clearImages();
            updateFormState({ selectedImages: [] });
            setMobileEnabled(false);

            // Also clear selected files if asset upload is enabled
            if (isUploadAssetEnabled) {
                setSelectedFiles([]);
            }

            // Update tab state synchronously
            updateTabState(tabId, finalTabStateData);

            // Update tabs synchronously
            setTabs((prevTabs) => {
                const tabExists = prevTabs.some(tab => tab.id === tabId);
                if (tabExists) {
                    return prevTabs.map((tab) =>
                        tab.id === tabId ? { ...tab, path: "/chat", title: "Setting up Task", state: finalTabStateData } : tab
                    );
                } else {
                    // Create new tab
                    return [...prevTabs, {
                        id: tabId,
                        title: "Setting up Task",
                        path: "/chat",
                        state: finalTabStateData
                    }];
                }
            });

            // Set active tab synchronously
            setActiveTab(tabId);
            onClearGithub();
            onClearTabId();

        } catch (error) {
            console.error("Error in handleTaskSubmission:", error);
            toast({
                title: "Error",
                description: "Failed to create task. Please try again.",
                variant: "destructive",
            });

            trackTaskCreation('abandoned', {
                userId: user?.id,
                reason: 'submission_error',
                error: String(error),
                tabId
            });
        } finally {
            submissionInProgress.current = false;
            setIsSubmitting(false);
        }
    }, 700);

    const handleSubmit = async () => {
        if (session && user) {
            // Store images in tab state immediately before triggering submission
            // This ensures images are available when ChatScreen's useEffect triggers submitJOB
            const currentTabState = getTabState(tabId);

            // Convert selected files to base64 format for payload inclusion
            const base64Images = [];
            if (selectedFiles && selectedFiles.length > 0) {
                // Convert File objects to base64 format
                for (const file of selectedFiles) {
                    if (file.type.startsWith('image/')) {
                        try {
                            const base64Data = await new Promise((resolve, reject) => {
                                const reader = new FileReader();
                                reader.onload = () => {
                                    const result = reader.result as string;
                                    const base64 = result.split(',')[1]; // Remove data:image/...;base64, prefix
                                    resolve(base64);
                                };
                                reader.onerror = reject;
                                reader.readAsDataURL(file);
                            });

                            base64Images.push({
                                mime_type: file.type,
                                img_base64: base64Data
                            });
                        } catch (error) {
                            console.error("Error converting file to base64:", error);
                        }
                    }
                }
            }

            // Combine legacy selectedImages with new base64Images from files
            const allBase64Images = [
                ...(formState.selectedImages || []),
                ...base64Images
            ];

            const updatedTabState = {
                ...currentTabState,
                selectedImages: allBase64Images,
                base64_image_list: allBase64Images.length > 0 ? allBase64Images : undefined,
                needsApiCall: true // This will trigger ChatScreen's useEffect
            };

            updateTabState(tabId, updatedTabState);

            // Wait a moment to ensure tab state is updated before triggering job submission
            setTimeout(() => {
                debouncedSubmit();
            }, 100);
        } else {
            setIsModalOpen(true);
        }
    };

    // Determine banner display logic
    const shouldShowBanners = referralEnabled || customAgentsEnabled;
    const shouldShowReferralBanner = referralEnabled && (!customAgentsEnabled || currentBanner === 'referral');
    const shouldShowCustomAgentsBanner = customAgentsEnabled && (!referralEnabled || currentBanner === 'customAgents');

    // Determine background gradient based on current banner
    const getBannerBackground = () => {
        if (!shouldShowBanners) return "none";
        if (referralEnabled && customAgentsEnabled) {
            return currentBanner === 'referral'
                ? "linear-gradient(to bottom, rgba(128, 255, 249, 0.04) 0%, rgba(128, 255, 249, 0.1) 100%)"
                : "linear-gradient(to bottom, rgba(221, 221, 230, 0.04) 0%, rgba(221, 221, 230, 0.1) 100%)";
        }
        return referralEnabled
            ? "linear-gradient(to bottom, rgba(128, 255, 249, 0.04) 0%, rgba(128, 255, 249, 0.1) 100%)"
            : "linear-gradient(to bottom, rgba(221, 221, 230, 0.04) 0%, rgba(221, 221, 230, 0.1) 100%)";
    };

    
    // Render functions
    const renderTaskDescription = () => (
        <div>
            <div className="relative">
                <TextareaWithAttachmentV2
                    value={formState.taskInput}
                    onChange={(e) => {
                        updateFormState({ taskInput: e.target.value });
                        // Track task input changes (debounced)
                        if (e.target.value.length % 50 === 0) {
                            trackTaskCreation('started', {
                                userId: user?.id,
                                taskInputLength: e.target.value.length,
                                isTyping: true,
                                tabId
                            });
                        }
                    }}
                    handleExperimentalChange={handleExperimentChange}
                    experimentalEnabled={experimental}
                    className="relative bg-[#131314] rounded-t-[12px] rounded-b-[12px] text-[#DDDDE6]
                    placeholder:text-[#666] md:text-base focus-visible:ring-1 focus:ring-1 z-10"
                    onAttachClick={handleFilePicker}
                    attachIcon={AttachIcon}
                    attachTooltip={isUploadAssetEnabled ? "Attach files & images" : "Attach images (max 5 images, 5MB each)"}
                    isUploadAssetEnabled={isUploadAssetEnabled}
                    onPaste={(e) => {
                        const items = e.clipboardData?.items;
                        if (!items) return;

                        if (isUploadAssetEnabled) {
                            // When asset upload is enabled, accept all file types
                            const fileItems = Array.from(items).filter(item => item.kind === 'file');
                            if (fileItems.length === 0) return;

                            e.preventDefault();
                            const files = fileItems.map(item => item.getAsFile()).filter(Boolean) as File[];
                            if (files.length > 0) {
                                handleAssetSelection(files);
                            }
                        } else {
                            // Legacy behavior: only images
                            const imageItems = Array.from(items).filter(item => item.type.startsWith('image/'));
                            if (imageItems.length === 0) return;

                            e.preventDefault();
                            const files = imageItems.map(item => item.getAsFile()).filter(Boolean) as File[];
                            if (files.length > 0) {
                                imageAttachments.handleImageSelect(files);
                            }
                        }
                    }}
                    handleSettingsClick={() => {
                        if (isTransitioning) return;
                        if (showGithubSettings) {
                            setIsTransitioning(true);
                            setShowGithubSettings(false);
                            setTimeout(() => {
                                setShowControls(!showControls);
                                setIsTransitioning(false);
                            }, 300);
                        } else {
                            setShowControls(!showControls);
                        }
                    }}
                    showControls={showControls}
                    onSubmit={handleSubmit}
                    disabled={isSubmitting || !formState.taskInput || isInputDisabled()}
                    isSubmitting={isSubmitting}
                    showGithubSettings={showGithubSettings}
                    handleGithubSettings={() => {
                        if (!session) {
                            setIsModalOpen(true);
                            return;
                        }
                        if (isTransitioning) return;
                        if (showControls) {
                            setIsTransitioning(true);
                            setShowControls(false);
                            setTimeout(() => {
                                setShowGithubSettings(!showGithubSettings);
                                setIsTransitioning(false);
                            }, 300);
                        } else {
                            setShowGithubSettings(!showGithubSettings);
                        }
                    }}
                    hasUserConnectedGithub={hasUserConnectedGithub}
                    githubUrl={formState.githubUrl}
                    onClearGithubUrl={onClearGithub}
                    // User prompts props
                    userPrompts={userPromptsData?.user_prompts || []}
                    isLoadingUserPrompts={isLoadingUserPrompts}
                    selectedCustomAgent={formState.selectedCustomAgent}
                    onCustomAgentChange={(agentId) => updateFormState({ selectedCustomAgent: agentId })}
                    shouldShowUserPrompts={shouldFetchUserPrompts}
                    mobileEnabled={mobileEnabled}
                    onMobileEnabled={setMobileEnabled}
                />
            </div>

            {/* Selected Images/Files Preview */}
            {(isUploadAssetEnabled ? selectedFiles.length > 0 : formState.selectedImages.length > 0) && (
                <div className="mt-[-20px] max-w-[94%] mx-auto bg-[#1D1D1F] border border-[#222] rounded-lg p-3">
                    <div className="flex flex-wrap gap-3 pt-4">
                        {(formState.loadingImages || isProcessingFiles) && (
                            <div className="flex items-center justify-center w-full py-4">
                                <Loader2 className="h-6 w-6 text-[#999] animate-spin mr-2" />
                                <span className="text-[#999]">
                                    {isProcessingFiles ? "Processing and compressing files..." : "Processing files..."}
                                </span>
                            </div>
                        )}
                        {!formState.loadingImages && !isProcessingFiles && (
                            isUploadAssetEnabled ? (
                                // Show selected files when asset upload is enabled
                                selectedFiles.map((file, index) => {
                                    const isImage = file.type.startsWith('image/');
                                    const previewUrl = isImage ? URL.createObjectURL(file) : null;
                                    return (
                                        <div
                                            key={`${file.name}-${index}`}
                                            className="relative w-12 h-12 rounded-md border border-[#333] group"
                                        >
                                            {isImage && previewUrl ? (
                                                <img
                                                    src={previewUrl}
                                                    alt={file.name}
                                                    className="w-full h-full object-cover z-[1] rounded-md"
                                                />
                                            ) : (
                                                <div className="w-full h-full bg-[#2A2A2C] rounded-md flex items-center justify-center z-[1]">
                                                    <span className="text-[#999] text-xs font-mono">
                                                        {file.name.split('.').pop()?.toUpperCase() || 'FILE'}
                                                    </span>
                                                </div>
                                            )}
                                            <button
                                                type="button"
                                                onClick={() => handleRemoveSelectedFile(index)}
                                                className="absolute top-[-8px] right-[-8px] bg-transparent z-[2] rounded-full p-1 transition-opacity"
                                                aria-label="Remove file"
                                            >
                                                <div className="p-1 bg-white rounded-full">
                                                    <img src={AttachmentCross} alt="Remove file" className="w-3 h-3" />
                                                </div>
                                            </button>
                                        </div>
                                    );
                                })
                            ) : (
                                // Show legacy images when asset upload is disabled
                                formState.selectedImages.map((image, index) => (
                                    <div
                                        key={index}
                                        className="relative w-12 h-12 rounded-md border border-[#333] group"
                                    >
                                        <img
                                            src={`data:${image.mime_type};base64,${image.img_base64}`}
                                            alt={`Selected image ${index + 1}`}
                                            className="w-full h-full object-cover z-[1] rounded-md"
                                        />
                                        <button
                                            type="button"
                                            onClick={() => imageAttachments.removeImage(index)}
                                            className="absolute top-[-8px] right-[-8px] bg-transparent z-[2] rounded-full p-1 transition-opacity"
                                            aria-label="Remove image"
                                        >
                                            <div className="p-1 bg-white rounded-full">
                                                <img src={AttachmentCross} alt="Remove image" className="w-3 h-3" />
                                            </div>
                                        </button>
                                    </div>
                                ))
                            )
                        )}
                    </div>
                </div>
            )}
        </div>
    );

    const renderAdvancedControls = () => (
      <div className="w-full bg-[#0F181A] border border-[#66EAFF1F] rounded-lg transition-colors mt-3">
        <div className="flex items-center justify-between w-full gap-4 px-5 pt-5">
          <div className="flex items-center gap-2">
            <img
              alt="Advanced Controls"
              src={AdvancedControls}
              className="w-6 h-6"
            />
            <span className="text-[#66EAFF] font-brockmann font-medium">
              Advanced Controls
            </span>
          </div>
          <img
            alt="Close"
            src={CloseSVG}
            className="w-5 h-5 cursor-pointer"
            onClick={() => {
              if (!isTransitioning) {
                setShowControls(false);
              }
            }}
          />
        </div>

        <div className="grid grid-cols-1 gap-5 px-5 py-5 md:grid-cols-2">
          {/* Docker Image Selector */}
          {isFieldVisible("dockerImage") && (
            <DockerImageSelector
              selectedImage={formState.selectedImage}
              setSelectedImage={(image) =>
                updateFormState({ selectedImage: image })
              }
              globalConfig={globalConfig as any}
              label="Select Template"
              isCloudFlow={true}
            />
          )}

          {/* Budget Control */}
          {isFieldVisible('budget') && <div className="flex flex-col justify-between h-full">
            <p className="text-[#FFFFFF50] font-inter text-sm">Budget (ECU)</p>
            <div className="relative flex items-center">
              <button
                type="button"
                title="Decrease budget"
                onClick={() => {
                  const current = parseInt(formState.perInstanceCostLimit);
                  if (current > 1) {
                    updateFormState({
                      perInstanceCostLimit: (current - 1).toString(),
                    });
                  }
                }}
                className="w-[40px] h-[40px] flex items-center justify-center absolute left-1 text-[#666] hover:text-[#999] transition-colors hover:bg-[#0F181A] rounded-sm"
              >
                <Minus className="h-6 w-6 text-[#DDDDE6]" />
              </button>
              <div className="w-full h-[56px] bg-[#FFFFFF0A] border border-[#ffffff12] rounded-lg text-[#DDDDE6] px-4 flex items-center justify-center hover:border-[#333]">
                <img
                  alt="Budget"
                  className="text-[#B4A456] w-5 h-5"
                  src={CoinIconSVG}
                />
                <input
                  type="number"
                  min="1"
                  step="1"
                  value={formState.perInstanceCostLimit}
                  onChange={(e) => {
                    if (e.target.value !== "0") {
                      updateFormState({ perInstanceCostLimit: e.target.value });
                    }
                  }}
                  disabled={configLoading}
                  placeholder={configLoading ? "Loading..." : " - - "}
                  className="w-20 bg-transparent text-left focus:outline-none text-[#DDDDE6] font-inter text-xl font-semibold leading-[120%] tracking-[-0.4px] [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                />
              </div>
              <button
                type="button"
                title="Increase budget"
                onClick={() => {
                  const current = parseInt(formState.perInstanceCostLimit);
                  updateFormState({
                    perInstanceCostLimit: (current + 1).toString(),
                  });
                }}
                className="w-[40px] h-[40px] flex items-center justify-center absolute right-1 text-[#666] hover:text-[#999] transition-colors hover:bg-[#0F181A] rounded-sm"
              >
                <Plus className="h-6 w-6 text-[#DDDDE6]" />
              </button>
            </div>
          </div> }
        </div>

        <div className="grid grid-cols-1 gap-5 px-5 py-5">
          {/* Agent Selector */}
          {isFieldVisible('agent') && <div className="flex flex-col space-y-2">
            <p className="text-[#FFFFFF50] font-inter text-sm">Agent</p>
            <Select
              value={formState.selectedAgent}
              onValueChange={(value: string) =>
                updateFormState({ selectedAgent: value })
              }
              disabled={configLoading}
            >
              <SelectTrigger className="w-full h-14 bg-[#FFFFFF0A] border border-[#ffffff12] rounded-lg text-[#DDDDE6] hover:border-[#333] transition-colors data-[state=open]:border-[#333]">
                {configLoading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Loading agents...</span>
                  </div>
                ) : (
                  <SelectValue placeholder="Select Agent" />
                )}
              </SelectTrigger>
              <SelectContent className="bg-[#131314] border border-[#222]">
                {globalConfig?.agent_names?.map((agent: any, index: number) => (
                  <SelectItem
                    key={`${agent.name}-${index}`}
                    value={agent.name}
                    className="text-[#DDDDE6] hover:bg-[#222] focus:bg-[#222]"
                  >
                    {agent.display_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>}

          {/* Base Model Selector */}
          {isFieldVisible('baseModel') && <div className="flex flex-col space-y-2">
            <p className="text-[#FFFFFF50] font-inter text-sm">Base Model</p>
            <Select
              value={formState.selectedModel}
              onValueChange={(value: string) =>
                updateFormState({ selectedModel: value })
              }
              disabled={configLoading}
            >
              <SelectTrigger className="w-full h-12 bg-[#FFFFFF0A] border border-[#ffffff12] rounded-lg text-[#DDDDE6] hover:border-[#ffffff30] transition-colors data-[state=open]:border-[#ffffff30]">
                <div className="flex items-center gap-2">
                  <img alt="" src={CuteAIIcon} className="w-5 h-5" />
                  {configLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Loading models...</span>
                    </div>
                  ) : (
                    <SelectValue placeholder="Select Model" />
                  )}
                </div>
              </SelectTrigger>
              <SelectContent className="bg-[#131314] border border-[#222]">
                {globalConfig?.model_list?.map((model: any) => (
                  <SelectItem
                    key={model.name}
                    value={model.name}
                    className="text-[#DDDDE6] hover:bg-[#222] cursor-pointer"
                  >
                    {model.display_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div> }

          {/* Skills Selector */}
          {isFieldVisible('skills') && <SkillsSelector
            selectedSkills={formState.selectedSkills}
            setSelectedSkills={(skills) =>
              updateFormState({ selectedSkills: skills })
            }
            skills={skills}
            loadingSkills={false}
            label="Skills"
          /> }
        </div>
      </div>
    );

    if (!globalConfig) {
        return (
            <div className="flex items-center min-h-[300px] justify-center w-full h-full">
                <Loader2 className="w-8 h-8 text-white opacity-25 animate-spin" />
            </div>
        );
    }

    return (
        <>
            <AuthModal
                enableWelcomeModal={true}
                open={isModalOpen}
                onOpenChange={setIsModalOpen}
                defaultView="signup"
                onSuccess={handleLoginSuccess}
            />
            <CustomAgentsModal
                isOpen={isCustomAgentsModalOpen}
                onOpenChange={setIsCustomAgentsModalOpen}
            />
            <Tooltip.Provider delayDuration={200}>
                <div className="relative">
                    <div className="w-full max-w-4xl p-6 mx-auto md:pt-10">
                        {/* Main Task Description */}
                        <motion.div
                            initial={{ opacity: 0, y: -20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{
                                duration: 0.3,
                                ease: "easeOut"
                            }}
                        >
                           <div
                               className={cn("bg-[#FFFFFF0D] rounded-[14px] ", shouldShowBanners ? "p-1" : "p-[0px] border border-[#2C2C2D]")}
                               style={{background: getBannerBackground()}}
                               onMouseEnter={() => setIsBannerHovered(true)}
                               onMouseLeave={() => setIsBannerHovered(false)}
                           >
                               <div className="relative">
                                   <AnimatePresence mode="wait">
                                       {shouldShowReferralBanner && (
                                           <motion.div
                                               key="referral-banner"
                                               initial={{ opacity: 0, y: -10 }}
                                               animate={{ opacity: 1, y: 0 }}
                                               exit={{ opacity: 0, y: 10 }}
                                               transition={{ duration: 0.3, ease: "easeInOut" }}
                                           >
                                               <ReferralBanner
                                                   onOpenReferralModal={() => setIsReferralModalOpen(true)}
                                               />
                                           </motion.div>
                                       )}
                                       {shouldShowCustomAgentsBanner && (
                                           <motion.div
                                               key="custom-agents-banner"
                                               initial={{ opacity: 0, y: -10 }}
                                               animate={{ opacity: 1, y: 0 }}
                                               exit={{ opacity: 0, y: 10 }}
                                               transition={{ duration: 0.3, ease: "easeInOut" }}
                                           >
                                               <CustomAgentsBanner
                                                   onOpenCustomAgentsModal={() => setIsCustomAgentsModalOpen(true)}
                                               />
                                           </motion.div>
                                       )}
                                   </AnimatePresence>

                                   {/* Progress indicator - only show when both banners are enabled and not hovered */}
                                   {/* {referralEnabled && customAgentsEnabled && !isBannerHovered && (
                                       <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-[#FFFFFF10] rounded-b-[14px] overflow-hidden">
                                           <motion.div
                                               className="h-full bg-gradient-to-r from-[#80FFF9] to-[#DD99FF]"
                                               style={{ width: `${bannerProgress}%` }}
                                               initial={{ width: 0 }}
                                               animate={{ width: `${bannerProgress}%` }}
                                               transition={{ duration: 0.1, ease: "linear" }}
                                           />
                                       </div>
                                   )} */}
                               </div>
                               {renderTaskDescription()}
                           </div>
                        </motion.div>

                        {/* Suggestion Chips */}
                        {(isUploadAssetEnabled ? selectedFiles.length === 0 : formState.selectedImages.length === 0) && (
                            <SuggestionChips
                                config={globalConfig as any}
                                showChips={showChips && !showControls && (isMobile || !showGithubSettings)}
                                taskInput={formState.taskInput}
                                onChipClick={handleChipClick}
                                onSurpriseMe={() => {}}
                            />
                        )}

                        {/* GitHub Settings */}
                        <AnimatePresence>
                            {!showControls && showGithubSettings && (
                                <motion.div
                                    initial={{ opacity: 0, height: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, height: 'auto', scale: 1 }}
                                    exit={{ opacity: 0, height: 0, scale: 0.8 }}
                                    transition={{ duration: 0.3, ease: "easeInOut" }}
                                >
                                    <div className="w-full bg-[#131314] mt-3">
                                        <RepositorySelectorV2
                                            ref={repositorySelectorRef}
                                            githubUrl={formState.githubUrl}
                                            branchName={formState.branchName}
                                            onGithubUrlChange={(url: string) => updateFormState({ githubUrl: url })}
                                            onBranchNameChange={(branch: string) => updateFormState({ branchName: branch })}
                                            onClose={() => {
                                                setShowGithubSettings(false);
                                            }}
                                        />
                                    </div>
                                </motion.div>
                            )}

                            {/* Advanced Controls */}
                            {showControls && !showGithubSettings && (
                                <motion.div
                                    initial={{ opacity: 0, height: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, height: 'auto', scale: 1 }}
                                    exit={{ opacity: 0, height: 0, scale: 0.8 }}
                                    transition={{ duration: 0.3, ease: "easeInOut" }}
                                >
                                    {renderAdvancedControls()}
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                </div>
            </Tooltip.Provider>
        </>
    );
}